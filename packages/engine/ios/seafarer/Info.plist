<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>BGTaskSchedulerPermittedIdentifiers</key>
		<array>
			<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		</array>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleDisplayName</key>
		<string>bSeafarer</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>bSeafarer</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(MARKETING_VERSION)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(CURRENT_PROJECT_VERSION)</string>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>LSSupportsOpeningDocumentsInPlace</key>
		<true />
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<false />
			<key>NSAllowsLocalNetworking</key>
			<true />
		</dict>
		<key>NSAppleMusicUsageDescription</key>
		<string>The app needs access to your music library to allow you to upload inspection recordings.
			This helps document issues found during vessel inspections.</string>
		<key>NSCameraUsageDescription</key>
		<string>The app requires camera access to capture inspection photos and attach them to reports
			for better documentation.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>We need your permission in order to provide a better map experience</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>The app needs microphone access to record voice notes during inspections for better
			reporting.</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>The app needs access to your photo library to allow you to upload inspection images and
			reports. This helps document issues found during vessel inspections.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>The app requires access to the photo library to select photos and attach them to reports
			for better documentation.</string>
		<key>UIAppFonts</key>
		<array>
			<string>Inter-Regular.ttf</string>
			<string>Inter-Black.ttf</string>
			<string>Inter-Bold.ttf</string>
			<string>Inter-ExtraBold.ttf</string>
			<string>Inter-ExtraLight.ttf</string>
			<string>Inter-Light.ttf</string>
			<string>Inter-Medium.ttf</string>
			<string>Inter-SemiBold.ttf</string>
			<string>Inter-Thin.ttf</string>
			<string>benefit-icons-v8.ttf</string>
		</array>
		<key>UIBackgroundModes</key>
		<array>
			<string>processing</string>
			<string>remote-notification</string>
		</array>
		<key>UIFileSharingEnabled</key>
		<true />
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>armv7</string>
		</array>
		<key>UIRequiresFullScreen</key>
		<true />
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportsDocumentBrowser</key>
		<true />
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false />
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
		<key>NSUserTrackingUsageDescription</key>
		<string>We use your data to provide a better ad experience tailored to you.</string>
	</dict>
</plist>