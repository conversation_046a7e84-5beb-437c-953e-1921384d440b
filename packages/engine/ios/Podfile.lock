PODS:
  - boost (1.83.0)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.73.11)
  - FBReactNativeSpec (0.73.11):
    - RCT-<PERSON>olly (= 2022.05.16.00)
    - RCTRequired (= 0.73.11)
    - RCTTypeSafety (= 0.73.11)
    - React-Core (= 0.73.11)
    - React-jsi (= 0.73.11)
    - ReactCommon/turbomodule/core (= 0.73.11)
  - Firebase/Analytics (11.5.0):
    - Firebase/Core
  - Firebase/Core (11.5.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.5.0)
  - Firebase/CoreOnly (11.5.0):
    - FirebaseCore (= 11.5.0)
  - Firebase/Crashlytics (11.5.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.5.0)
  - FirebaseAnalytics (11.5.0):
    - FirebaseAnalytics/AdIdSupport (= 11.5.0)
    - FirebaseCore (= 11.5)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.5.0):
    - FirebaseCore (= 11.5)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.5.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.5.0):
    - FirebaseCoreInternal (= 11.5)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.5.0):
    - FirebaseCore (= 11.5)
  - FirebaseCoreInternal (11.5.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.5.0):
    - FirebaseCore (= 11.5)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.5.0):
    - FirebaseCore (= 11.5)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseRemoteConfigInterop (11.12.0)
  - FirebaseSessions (11.5.0):
    - FirebaseCore (= 11.5)
    - FirebaseCoreExtension (= 11.5)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - fmt (6.2.1)
  - glog (0.3.5)
  - Google-Maps-iOS-Utils (4.1.0):
    - Google-Maps-iOS-Utils/Clustering (= 4.1.0)
    - Google-Maps-iOS-Utils/Geometry (= 4.1.0)
    - Google-Maps-iOS-Utils/GeometryUtils (= 4.1.0)
    - Google-Maps-iOS-Utils/Heatmap (= 4.1.0)
    - Google-Maps-iOS-Utils/QuadTree (= 4.1.0)
    - GoogleMaps
  - Google-Maps-iOS-Utils/Clustering (4.1.0):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps
  - Google-Maps-iOS-Utils/Geometry (4.1.0):
    - GoogleMaps
  - Google-Maps-iOS-Utils/GeometryUtils (4.1.0):
    - GoogleMaps
  - Google-Maps-iOS-Utils/Heatmap (4.1.0):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps
  - Google-Maps-iOS-Utils/QuadTree (4.1.0):
    - GoogleMaps
  - GoogleAppMeasurement (11.5.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.5.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.5.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.5.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.5.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMaps (7.4.0):
    - GoogleMaps/Maps (= 7.4.0)
  - GoogleMaps/Base (7.4.0)
  - GoogleMaps/Maps (7.4.0):
    - GoogleMaps/Base
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.73.11):
    - hermes-engine/Pre-built (= 0.73.11)
  - hermes-engine/Pre-built (0.73.11)
  - libevent (2.1.12)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - NextLevelSessionExporter (0.4.6)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RCT-Folly (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2022.05.16.00)
  - RCT-Folly/Default (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Fabric (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.73.11)
  - RCTTypeSafety (0.73.11):
    - FBLazyVector (= 0.73.11)
    - RCTRequired (= 0.73.11)
    - React-Core (= 0.73.11)
  - React (0.73.11):
    - React-Core (= 0.73.11)
    - React-Core/DevSupport (= 0.73.11)
    - React-Core/RCTWebSocket (= 0.73.11)
    - React-RCTActionSheet (= 0.73.11)
    - React-RCTAnimation (= 0.73.11)
    - React-RCTBlob (= 0.73.11)
    - React-RCTImage (= 0.73.11)
    - React-RCTLinking (= 0.73.11)
    - React-RCTNetwork (= 0.73.11)
    - React-RCTSettings (= 0.73.11)
    - React-RCTText (= 0.73.11)
    - React-RCTVibration (= 0.73.11)
  - React-callinvoker (0.73.11)
  - React-Codegen (0.73.11):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.11)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.11)
    - React-Core/RCTWebSocket (= 0.73.11)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.73.11)
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.11)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety (= 0.73.11)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.73.11)
    - ReactCommon
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.73.11):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.11)
    - React-debug (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-jsinspector (= 0.73.11)
    - React-logger (= 0.73.11)
    - React-perflogger (= 0.73.11)
    - React-runtimeexecutor (= 0.73.11)
  - React-debug (0.73.11)
  - React-Fabric (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.73.11)
    - React-Fabric/attributedstring (= 0.73.11)
    - React-Fabric/componentregistry (= 0.73.11)
    - React-Fabric/componentregistrynative (= 0.73.11)
    - React-Fabric/components (= 0.73.11)
    - React-Fabric/core (= 0.73.11)
    - React-Fabric/imagemanager (= 0.73.11)
    - React-Fabric/leakchecker (= 0.73.11)
    - React-Fabric/mounting (= 0.73.11)
    - React-Fabric/scheduler (= 0.73.11)
    - React-Fabric/telemetry (= 0.73.11)
    - React-Fabric/templateprocessor (= 0.73.11)
    - React-Fabric/textlayoutmanager (= 0.73.11)
    - React-Fabric/uimanager (= 0.73.11)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.73.11)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.73.11)
    - React-Fabric/components/modal (= 0.73.11)
    - React-Fabric/components/rncore (= 0.73.11)
    - React-Fabric/components/root (= 0.73.11)
    - React-Fabric/components/safeareaview (= 0.73.11)
    - React-Fabric/components/scrollview (= 0.73.11)
    - React-Fabric/components/text (= 0.73.11)
    - React-Fabric/components/textinput (= 0.73.11)
    - React-Fabric/components/unimplementedview (= 0.73.11)
    - React-Fabric/components/view (= 0.73.11)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired (= 0.73.11)
    - RCTTypeSafety (= 0.73.11)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.73.11)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-graphics (0.73.11):
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core/Default (= 0.73.11)
    - React-utils
  - React-hermes (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - RCT-Folly/Futures (= 2022.05.16.00)
    - React-cxxreact (= 0.73.11)
    - React-jsi
    - React-jsiexecutor (= 0.73.11)
    - React-jsinspector (= 0.73.11)
    - React-perflogger (= 0.73.11)
  - React-ImageManager (0.73.11):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.73.11):
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.73.11):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
  - React-jsiexecutor (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-perflogger (= 0.73.11)
  - React-jsinspector (0.73.11)
  - React-logger (0.73.11):
    - glog
  - React-Mapbuffer (0.73.11):
    - glog
    - React-debug
  - react-native-background-actions (4.0.1):
    - React-Core
  - react-native-blob-util (0.19.11):
    - React-Core
  - react-native-compressor (1.8.15):
    - glog
    - NextLevelSessionExporter
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-config (1.5.3):
    - react-native-config/App (= 1.5.3)
  - react-native-config/App (1.5.3):
    - React-Core
  - react-native-document-picker (8.2.0):
    - React-Core
  - react-native-google-maps (1.7.1):
    - Google-Maps-iOS-Utils (= 4.1.0)
    - GoogleMaps (= 7.4.0)
    - React-Core
  - react-native-image-picker (5.0.2):
    - React-Core
  - react-native-image-resizer (3.0.11):
    - React-Core
  - react-native-maps (1.7.1):
    - React-Core
  - react-native-netinfo (9.5.0):
    - React-Core
  - react-native-pager-view (5.4.25):
    - React-Core
  - react-native-pdf (6.7.7):
    - React-Core
  - react-native-photo-editor (1.1.6):
    - React-Core
    - react-native-photo-editor/ZLImageEditor (= 1.1.6)
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - react-native-photo-editor/ZLImageEditor (1.1.6):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-safe-area-context (3.4.1):
    - React-Core
  - react-native-slider (4.4.2):
    - React-Core
  - react-native-tracking-transparency (0.1.2):
    - React
  - react-native-video (5.2.1):
    - React-Core
    - react-native-video/Video (= 5.2.1)
  - react-native-video/Video (5.2.1):
    - React-Core
  - react-native-webview (11.26.1):
    - React-Core
  - React-nativeconfig (0.73.11)
  - React-NativeModulesApple (0.73.11):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.73.11)
  - React-RCTActionSheet (0.73.11):
    - React-Core/RCTActionSheetHeaders (= 0.73.11)
  - React-RCTAnimation (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.73.11):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon
  - React-RCTBlob (0.73.11):
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.73.11):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.73.11)
  - React-RCTNetwork (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.73.11):
    - React-Core/RCTTextHeaders (= 0.73.11)
    - Yoga
  - React-RCTVibration (0.73.11):
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - React-rncore (0.73.11)
  - React-runtimeexecutor (0.73.11):
    - React-jsi (= 0.73.11)
  - React-runtimescheduler (0.73.11):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.73.11):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - ReactCommon (0.73.11):
    - React-logger (= 0.73.11)
    - ReactCommon/turbomodule (= 0.73.11)
  - ReactCommon/turbomodule (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.11)
    - React-cxxreact (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-logger (= 0.73.11)
    - React-perflogger (= 0.73.11)
    - ReactCommon/turbomodule/bridging (= 0.73.11)
    - ReactCommon/turbomodule/core (= 0.73.11)
  - ReactCommon/turbomodule/bridging (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.11)
    - React-cxxreact (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-logger (= 0.73.11)
    - React-perflogger (= 0.73.11)
  - ReactCommon/turbomodule/core (0.73.11):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.11)
    - React-cxxreact (= 0.73.11)
    - React-jsi (= 0.73.11)
    - React-logger (= 0.73.11)
    - React-perflogger (= 0.73.11)
  - RNAudioRecorderPlayer (3.6.12):
    - React-Core
  - RNCAsyncStorage (2.1.0):
    - React-Core
  - RNCClipboard (1.14.2):
    - React-Core
  - RNCPicker (2.4.10):
    - React-Core
  - RNDateTimePicker (8.2.0):
    - React-Core
  - RNDeviceInfo (10.6.0):
    - React-Core
  - RNFBAnalytics (21.5.0):
    - Firebase/Analytics (= 11.5.0)
    - React-Core
    - RNFBApp
  - RNFBApp (21.5.0):
    - Firebase/CoreOnly (= 11.5.0)
    - React-Core
  - RNFBCrashlytics (21.5.0):
    - Firebase/Crashlytics (= 11.5.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.20.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNNotifee (7.8.0):
    - React-Core
    - RNNotifee/NotifeeCore (= 7.8.0)
  - RNNotifee/NotifeeCore (7.8.0):
    - React-Core
  - RNPermissions (3.10.1):
    - React-Core
  - RNReanimated (3.16.2):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.16.2)
    - RNReanimated/worklets (= 3.16.2)
  - RNReanimated/reanimated (3.16.2):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated/apple (= 3.16.2)
  - RNReanimated/reanimated/apple (3.16.2):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNReanimated/worklets (3.16.2):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNScreens (3.34.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - React-RCTImage
  - RNSound (0.11.2):
    - React-Core
    - RNSound/Core (= 0.11.2)
  - RNSound/Core (0.11.2):
    - React-Core
  - RNSoundPlayer (0.13.4):
    - React-Core
  - RNSVG (15.9.0):
    - React-Core
  - RNVectorIcons (9.2.0):
    - React-Core
  - RNZipArchive (7.0.1):
    - React-Core
    - RNZipArchive/Core (= 7.0.1)
    - SSZipArchive (~> 2.5.5)
  - RNZipArchive/Core (7.0.1):
    - React-Core
    - SSZipArchive (~> 2.5.5)
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - SocketRocket (0.6.1)
  - SSZipArchive (2.5.5)
  - VisionCamera (3.9.2):
    - React
    - React-callinvoker
    - React-Core
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../../../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../../../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../../../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../../../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - Google-Maps-iOS-Utils (from `https://github.com/Simon-TechForm/google-maps-ios-utils.git`, branch `feat/support-apple-silicon`)
  - GoogleMaps (= 7.4.0)
  - hermes-engine (from `../../../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - RCT-Folly (from `../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../../../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../../../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../../../node_modules/react-native/`)
  - React-callinvoker (from `../../../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../../../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../../../node_modules/react-native/`)
  - React-CoreModules (from `../../../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../../../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../../../node_modules/react-native/ReactCommon/react/debug`)
  - React-Fabric (from `../../../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../../../node_modules/react-native/ReactCommon`)
  - React-graphics (from `../../../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../../../node_modules/react-native/ReactCommon/hermes`)
  - React-ImageManager (from `../../../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../../../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../../../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../../../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../../../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-logger (from `../../../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../../../node_modules/react-native/ReactCommon`)
  - react-native-background-actions (from `../../../node_modules/react-native-background-actions`)
  - react-native-blob-util (from `../../../node_modules/react-native-blob-util`)
  - react-native-compressor (from `../../../node_modules/react-native-compressor`)
  - react-native-config (from `../../../node_modules/react-native-config`)
  - react-native-document-picker (from `../../../node_modules/react-native-document-picker`)
  - react-native-google-maps (from `../../../node_modules/react-native-maps`)
  - react-native-image-picker (from `../../../node_modules/react-native-image-picker`)
  - "react-native-image-resizer (from `../../../node_modules/@bam.tech/react-native-image-resizer`)"
  - react-native-maps (from `../../../node_modules/react-native-maps`)
  - "react-native-netinfo (from `../../../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../../../node_modules/react-native-pager-view`)
  - react-native-pdf (from `../../../node_modules/react-native-pdf`)
  - "react-native-photo-editor (from `../../../node_modules/@baronha/react-native-photo-editor`)"
  - react-native-render-html (from `../../../node_modules/react-native-render-html`)
  - react-native-safe-area-context (from `../../../node_modules/react-native-safe-area-context`)
  - "react-native-slider (from `../../../node_modules/@react-native-community/slider`)"
  - react-native-tracking-transparency (from `../../../node_modules/react-native-tracking-transparency`)
  - react-native-video (from `../../../node_modules/react-native-video`)
  - react-native-webview (from `../../../node_modules/react-native-webview`)
  - React-nativeconfig (from `../../../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../../../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../../../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../../../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../../../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../../../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../../../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../../../node_modules/react-native/React`)
  - React-RCTImage (from `../../../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../../../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../../../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../../../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../../../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../../../node_modules/react-native/Libraries/Vibration`)
  - React-rendererdebug (from `../../../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../../../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../../../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../../../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../../../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../../../node_modules/react-native/ReactCommon`)
  - RNAudioRecorderPlayer (from `../../../node_modules/react-native-audio-recorder-player`)
  - "RNCAsyncStorage (from `../../../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../../../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCPicker (from `../../../node_modules/@react-native-picker/picker`)"
  - "RNDateTimePicker (from `../../../node_modules/@react-native-community/datetimepicker`)"
  - RNDeviceInfo (from `../../../node_modules/react-native-device-info`)
  - "RNFBAnalytics (from `../../../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../../../node_modules/@react-native-firebase/app`)"
  - "RNFBCrashlytics (from `../../../node_modules/@react-native-firebase/crashlytics`)"
  - RNFS (from `../../../node_modules/react-native-fs`)
  - RNGestureHandler (from `../../../node_modules/react-native-gesture-handler`)
  - "RNNotifee (from `../../../node_modules/@notifee/react-native`)"
  - RNPermissions (from `../../../node_modules/react-native-permissions`)
  - RNReanimated (from `../../../node_modules/react-native-reanimated`)
  - RNScreens (from `../../../node_modules/react-native-screens`)
  - RNSound (from `../../../node_modules/react-native-sound`)
  - RNSoundPlayer (from `../../../node_modules/react-native-sound-player`)
  - RNSVG (from `../../../node_modules/react-native-svg`)
  - RNVectorIcons (from `../../../node_modules/react-native-vector-icons`)
  - RNZipArchive (from `../../../node_modules/react-native-zip-archive`)
  - VisionCamera (from `../../../node_modules/react-native-vision-camera`)
  - Yoga (from `../../../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - fmt
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUtilities
    - libevent
    - libwebp
    - nanopb
    - NextLevelSessionExporter
    - PromisesObjC
    - PromisesSwift
    - SDWebImage
    - SDWebImageWebPCoder
    - SocketRocket
    - SSZipArchive

EXTERNAL SOURCES:
  boost:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../../../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../../../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/glog.podspec"
  Google-Maps-iOS-Utils:
    :branch: feat/support-apple-silicon
    :git: https://github.com/Simon-TechForm/google-maps-ios-utils.git
  hermes-engine:
    :podspec: "../../../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-04-29-RNv0.73.8-644c8be78af1eae7c138fa4093fb87f0f4f8db85
  RCT-Folly:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../../../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../../../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../../../node_modules/react-native/"
  React-callinvoker:
    :path: "../../../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../../../node_modules/react-native/"
  React-CoreModules:
    :path: "../../../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../../../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../../../node_modules/react-native/ReactCommon/react/debug"
  React-Fabric:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-graphics:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../../../node_modules/react-native/ReactCommon/hermes"
  React-ImageManager:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../../../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../../../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../../../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../../../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-logger:
    :path: "../../../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../../../node_modules/react-native/ReactCommon"
  react-native-background-actions:
    :path: "../../../node_modules/react-native-background-actions"
  react-native-blob-util:
    :path: "../../../node_modules/react-native-blob-util"
  react-native-compressor:
    :path: "../../../node_modules/react-native-compressor"
  react-native-config:
    :path: "../../../node_modules/react-native-config"
  react-native-document-picker:
    :path: "../../../node_modules/react-native-document-picker"
  react-native-google-maps:
    :path: "../../../node_modules/react-native-maps"
  react-native-image-picker:
    :path: "../../../node_modules/react-native-image-picker"
  react-native-image-resizer:
    :path: "../../../node_modules/@bam.tech/react-native-image-resizer"
  react-native-maps:
    :path: "../../../node_modules/react-native-maps"
  react-native-netinfo:
    :path: "../../../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../../../node_modules/react-native-pager-view"
  react-native-pdf:
    :path: "../../../node_modules/react-native-pdf"
  react-native-photo-editor:
    :path: "../../../node_modules/@baronha/react-native-photo-editor"
  react-native-render-html:
    :path: "../../../node_modules/react-native-render-html"
  react-native-safe-area-context:
    :path: "../../../node_modules/react-native-safe-area-context"
  react-native-slider:
    :path: "../../../node_modules/@react-native-community/slider"
  react-native-tracking-transparency:
    :path: "../../../node_modules/react-native-tracking-transparency"
  react-native-video:
    :path: "../../../node_modules/react-native-video"
  react-native-webview:
    :path: "../../../node_modules/react-native-webview"
  React-nativeconfig:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../../../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../../../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../../../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../../../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../../../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../../../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../../../node_modules/react-native/React"
  React-RCTImage:
    :path: "../../../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../../../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../../../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../../../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../../../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../../../node_modules/react-native/Libraries/Vibration"
  React-rendererdebug:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../../../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../../../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../../../node_modules/react-native/ReactCommon"
  RNAudioRecorderPlayer:
    :path: "../../../node_modules/react-native-audio-recorder-player"
  RNCAsyncStorage:
    :path: "../../../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../../../node_modules/@react-native-clipboard/clipboard"
  RNCPicker:
    :path: "../../../node_modules/@react-native-picker/picker"
  RNDateTimePicker:
    :path: "../../../node_modules/@react-native-community/datetimepicker"
  RNDeviceInfo:
    :path: "../../../node_modules/react-native-device-info"
  RNFBAnalytics:
    :path: "../../../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../../../node_modules/@react-native-firebase/app"
  RNFBCrashlytics:
    :path: "../../../node_modules/@react-native-firebase/crashlytics"
  RNFS:
    :path: "../../../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../../../node_modules/react-native-gesture-handler"
  RNNotifee:
    :path: "../../../node_modules/@notifee/react-native"
  RNPermissions:
    :path: "../../../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../../../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../../../node_modules/react-native-screens"
  RNSound:
    :path: "../../../node_modules/react-native-sound"
  RNSoundPlayer:
    :path: "../../../node_modules/react-native-sound-player"
  RNSVG:
    :path: "../../../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../../../node_modules/react-native-vector-icons"
  RNZipArchive:
    :path: "../../../node_modules/react-native-zip-archive"
  VisionCamera:
    :path: "../../../node_modules/react-native-vision-camera"
  Yoga:
    :path: "../../../node_modules/react-native/ReactCommon/yoga"

CHECKOUT OPTIONS:
  Google-Maps-iOS-Utils:
    :commit: 35d05d1eeb65682c1b271f4f3760d814fd946aa1
    :git: https://github.com/Simon-TechForm/google-maps-ios-utils.git

SPEC CHECKSUMS:
  boost: d3f49c53809116a5d38da093a8aa78bf551aed09
  DoubleConversion: fea03f2699887d960129cc54bba7e52542b6f953
  FBLazyVector: b46891061bfe0a9b07f601813114c8653a72a45c
  FBReactNativeSpec: 68b6a0ee435ac2626dbd6a3e736cc2d415a30f40
  Firebase: 7a56fe4f56b5ab81b86a6822f5b8f909ae6fc7e2
  FirebaseAnalytics: 2f4a11eeb7a0e9c6fcf642d4e6aaca7fa4d38c28
  FirebaseCore: 93abc05437f8064cd2bc0a53b768fb0bc5a1d006
  FirebaseCoreExtension: ddb2eb987f736b714d30f6386795b52c4670439e
  FirebaseCoreInternal: f47dd28ae7782e6a4738aad3106071a8fe0af604
  FirebaseCrashlytics: 94c11c3bf296fde8c18f2c9f8e76bd9349227038
  FirebaseInstallations: d8063d302a426d114ac531cd82b1e335a0565745
  FirebaseRemoteConfigInterop: 82b81fd06ee550cbeff40004e2c106daedf73e38
  FirebaseSessions: b252b3f91a51186188882ea8e7e1730fc1eee391
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: c5d68082e772fa1c511173d6b30a9de2c05a69a2
  Google-Maps-iOS-Utils: 244f1ad05bdb625425d7d4a04b28bcb63dd61fd8
  GoogleAppMeasurement: ee5c2d2242816773fbf79e5b0563f5355ef1c315
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMaps: 032f676450ba0779bd8ce16840690915f84e57ac
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  hermes-engine: d992945b77c506e5164e6a9a77510c9d57472c59
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  NextLevelSessionExporter: 4d8aa5e617f1c709724f2453efe5d4628480f65a
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RCT-Folly: cd21f1661364f975ae76b3308167ad66b09f53f5
  RCTRequired: 415e56f7c33799a6483e41e4dce607f3daf1e69b
  RCTTypeSafety: e984a88e713281c2d8c2309a1a6d2775af0107ae
  React: ab885684e73c5f659bad63446a977312fd3d1ecb
  React-callinvoker: 50a2d1ce3594637c700401ba306373321231eb71
  React-Codegen: 82347e020719c7de190ddadd16fb8f5bcf4a9a52
  React-Core: d5166294382484f57e25dfde05ba00596703d51c
  React-CoreModules: 459534f8112ee73e94f04f5e58276b3d236efd16
  React-cxxreact: d5716540fd97df323792ef1d227f50515fb3e1a8
  React-debug: 660486c6bbf3fd9a3d36082b6c3e2d247bc09b41
  React-Fabric: 3412294ff4fad19c5946892a786d7804f7e76988
  React-FabricImage: 8b3316ad5c87d9f2388911570126808abef45ad8
  React-graphics: cf4ce028912bd9939b46f43924c5b7c85b5a7591
  React-hermes: 59ff965e45955d66977a23d51fe9235b44a09bd4
  React-ImageManager: 03a2aac0c6c83239a68fa1b72ba167697aca689d
  React-jserrorhandler: 262542e2d31b6dcd9579669c76d42dfe270a8aa5
  React-jsi: 36f85df7d83197707e9fd9320d857eac616e6df3
  React-jsiexecutor: a68ea442fd94c7ecf5d9355bde2443f0241531d9
  React-jsinspector: a98428936fb888cc15d857226a26d9ac0a668a0e
  React-logger: 6e4873d1f9c54cca30f6c91a6617f8c91b75ba4c
  React-Mapbuffer: f950f61557aecfb4beb33c26a7b709f4491cfbd7
  react-native-background-actions: 48e6bad9e2a47e3b04858634c5a05ea11062f680
  react-native-blob-util: f7234c91ad0e3faeee51b3edee80b61553f74993
  react-native-compressor: f15fc293c4a8a48365b3758452d66e86c082c363
  react-native-config: ea75335a7cca1d3326de1da384227e580a7c082e
  react-native-document-picker: 599581e52c3fe8f9a9a2d9a70b0e285199327a08
  react-native-google-maps: 508e6cc03c4d6af31f1d723439597ff1b2b43775
  react-native-image-picker: 374592cecccdba7a7ddf6f5388b4f5a41c5fe71e
  react-native-image-resizer: 24c5d06fae2176dc0caed4b6396e02befb44064a
  react-native-maps: 38394e3f4258d7fe8f1241c2624b91acf35e2dd5
  react-native-netinfo: 26560022f28c06d8ef00a9ff1e03beefbbb60c2d
  react-native-pager-view: 873aef831fb4fe6e1a2e1ba7a79857e240dea380
  react-native-pdf: 6a51a22ccefb23eb93298771e4bf090913e86d70
  react-native-photo-editor: 386600fd6cc11b23b3a745944482db2d74a96dae
  react-native-render-html: 5afc4751f1a98621b3009432ef84c47019dcb2bd
  react-native-safe-area-context: 667324e20fb3dd9c39c12d6036675ed90099bcd5
  react-native-slider: 3051c537444d711b566d1e4d681c6cd35081ae6f
  react-native-tracking-transparency: 15eb319f2b982070eb9831582af27d87badfa624
  react-native-video: 2aad0d963bf3952bd9ebb2f53fab799338e8e202
  react-native-webview: ec195db71ebf55705d7b46a7da5f1b1746bb7efd
  React-nativeconfig: 6178939b2ac9010dd2f9f994aab3ab11fa68fbf3
  React-NativeModulesApple: eea6abc6e6ea549faa10d867e44b3eba2d8cec3d
  React-perflogger: 3887a05940ccd34a83457fd153fdeda509b31737
  React-RCTActionSheet: 2f42b4797374b53e93b65c79eaa8a0d292e255ac
  React-RCTAnimation: 5639dcd418b798b28e9caacaed18ff5472454837
  React-RCTAppDelegate: 37d3142bfa7cb9f2f8cd41feedc6b50c95986029
  React-RCTBlob: e9f735bb085c6da208dd138bd4bfd294a52e3a86
  React-RCTFabric: 1a7f59a4034e51c4852b7d01ab2832ab42a62f36
  React-RCTImage: cc82df2b50bbdc1a0a0b19bf6fc16dd321eb8f0f
  React-RCTLinking: 3d7900f52ecf03bb2522d7b94d8d16cce776294a
  React-RCTNetwork: ac25c15ee52eb4aadb510afe68db0d1f949c45fa
  React-RCTSettings: 013301fe7304ff06acca5287f6f754c9fa2b63b7
  React-RCTText: ba6997c26241ea0b36ec8f98502a4ed45cab18e4
  React-RCTVibration: b5ba13ed0909d2f2614ed99b067543e6ea0d7c37
  React-rendererdebug: 50602bce0c403157f1ac87dce8600e63a3f7bce9
  React-rncore: 8172cb1a801b7473d87d1563fa8ff49cc222ac07
  React-runtimeexecutor: 2fd27b921f664e66d903ee274dcda55cc0b2cb2e
  React-runtimescheduler: f9d6540afe0a726320afe26cecf6189218b0b5a6
  React-utils: d1465452d47b2600e7e00bbe75651bef04f24342
  ReactCommon: 1102fc1d78fbfc56c57123e18d3acc14378f1403
  RNAudioRecorderPlayer: 11df0c7b614e9767ef24d896465c3a758c592de7
  RNCAsyncStorage: c91d753ede6dc21862c4922cd13f98f7cfde578e
  RNCClipboard: 47593431a9e763a71950798683b28f691f16b98d
  RNCPicker: 43a6a73efbb33d71640146070cc1720f1f75f5d6
  RNDateTimePicker: 818460dc31b0dc5ec58289003e27dd8d022fb79c
  RNDeviceInfo: e38f15f203e786b0a11bd7232d4b79ebcbb94a7c
  RNFBAnalytics: 0d3122e0f57617b331a150871b1df41c040ba30d
  RNFBApp: 7098ba4422cd4678d6361fdda37314d3bb3a1eb1
  RNFBCrashlytics: 67888208338df5f287aa36e6d6de7790235f9380
  RNFS: 89de7d7f4c0f6bafa05343c578f61118c8282ed8
  RNGestureHandler: 5d69ed5e1c8347ff745e4028a553c1fb700d7b07
  RNNotifee: c29575665bc5af1a664c1459560d082a04b8cf79
  RNPermissions: e5390dcdf8a8735c2f2edc7c7e05fa1fcdb1e202
  RNReanimated: a58a81b877dcf114f3854fc3e99177137e6624d8
  RNScreens: da54176e30d05e30cf6d4dc7c1b72dd50e0513bb
  RNSound: 314cc5226453ef4a3314a196c65e8a65e5106a7b
  RNSoundPlayer: 4c87665c7520851d58e66403f87d58e233cb51fa
  RNSVG: bb4bfcb8ec723a6f34b074a1b7cd40ee35246fe5
  RNVectorIcons: 5784330be9dddb5474e8b378d5f6947996c84e55
  RNZipArchive: f4a5af907d1581e995c879e34799be35c6bc3e21
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  SSZipArchive: c69881e8ac5521f0e622291387add5f60f30f3c4
  VisionCamera: 6a90992822a293947af96e77618933405a417871
  Yoga: d8b0f242670c8d28249d3aab276ca1a3b8db34f5

PODFILE CHECKSUM: 67fa22bdb50b69154e3e8133abf889b981aa524f

COCOAPODS: 1.16.2
