# Verify unread email count badges in folder list
# This test checks that folders with unread emails display a numerical badge with the exact count

appId: gr.benefit.bseafarer
---
# Clear state and launch app
- clearState
- launchApp

# Login if needed
- tapOn: "URL *"
- inputText: "https://bteam.benmaritime.eu/hermeswebapi"
- tapOn: "Usesrname *"
- inputText: "admin"
- tapOn: "Password *"
- inputText: "Bnft!123"
- tapOn: "Submit"
# Wait for the loader animation and screen transition
# - waitForAnimationToEnd
# - extendedWaitUntil:
#     id: "inbox--composeIcon"
- extendedWaitUntil:
    visible: "All" # or any other selector
    timeout: 40000 # Timeout in milliseconds

# Open burger menu to see folder list
- tapOn:
    id: "inbox__header--burgerIcon"

# Verify that Inbox folder is visible
- assertVisible: "Incoming"
# Tap on the Incoming folder
- tapOn: "Incoming"

# Take a screenshot to help debug what's actually visible
# This will show if there are parentheses with a number next to the Inbox folder
- takeScreenshot: "folder_list_screenshot"
# Test complete - we've verified that the Inbox folder is visible
