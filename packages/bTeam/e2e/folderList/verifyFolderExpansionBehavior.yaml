# Verify folder expansion behavior
# This test checks that:
# 1. Only top-level folders are initially loaded
# 2. When a parent folder is expanded, its subfolders are loaded and displayed
# 3. Subfolders are not loaded until their parent folder is expanded

appId: gr.benefit.bseafarer
---
# Clear state and launch app
- clearState
- launchApp

# Login if needed
- tapOn: "URL *"
- inputText: "https://bteam.benmaritime.eu/hermeswebapi"
- tapOn: "Usesrname *"
- inputText: "admin"
- tapOn: "Password *"
- inputText: "Bnft!123"
- tapOn: "Submit"
- extendedWaitUntil:
    visible: "All" # or any other selector
    timeout: 40000 # Timeout in milliseconds

# Open burger menu to see folder list
- tapOn:
    id: "inbox__header--burgerIcon"

# Verify that only top-level folders are initially loaded
# Check for the presence of top-level folders using the correct test IDs
- assertVisible:
    id: "folder-item-0"
- assertVisible:
    id: "folder-item-1"
- assertVisible:
    id: "folder-item-2"
- assertVisible:
    id: "folder-item-3"

# Take a screenshot of the initial folder list
- takeScreenshot: "initial_folder_list"

# Find a parent folder that can be expanded (e.g., "Frequently Used Folders")
# We'll use a common parent folder that should have subfolders
# Look for a folder with an expand button
- tapOn:
    id: "folder-item-4-expandButton"

# Take a screenshot after expanding the parent folder
- takeScreenshot: "expanded_parent_folder"

# Verify that subfolders are now visible
# These assertions will depend on the actual subfolders present
- assertVisible:
    id: "folder-item-4-child-0"

# Collapse the parent folder
- tapOn:
    id: "folder-item-4-expandButton"

# Take a screenshot after collapsing the parent folder
- takeScreenshot: "collapsed_parent_folder"

# Verify that another parent folder's subfolders are not loaded until expanded
# Try to find a subfolder that should not be visible yet
- assertNotVisible:
    id: "folder-item-5-child-0"

# Expand another parent folder (e.g., "Personal Folders")
- tapOn:
    id: "folder-item-5-expandButton"

# Take a screenshot after expanding the second parent folder
- takeScreenshot: "expanded_second_parent_folder"

# Verify that the second parent folder's subfolders are now visible
- assertVisible:
    id: "folder-item-5-child-0"
# Test complete - we've verified the folder expansion behavior
