import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { SEARCH_ENTITIES, SearchEntityType } from "../constants/searchEntities";

const SUGGESTIONS_SET_LIMIT = 50;

const initialState = {
  [SEARCH_ENTITIES.messages]: {
    searchSuggestions: [],
  },
  [SEARCH_ENTITIES.notifications]: {
    searchSuggestions: [],
  },
};

const searchSuggestionsSlice = createSlice({
  name: "bTeamSearchSuggestions",
  initialState,
  reducers: {
    addSearchSuggestion: (
      state,
      action: PayloadAction<{
        entityType: SearchEntityType;
        suggestion: string;
      }>
    ) => {
      const { entityType, suggestion } = action.payload;

      if (!state[entityType] || !suggestion.trim()) {
        return;
      }

      // Check if suggestion already exists, if so, remove it
      // Limit to max {SUGGESTIONS_SET_LIMIT} items
      state[entityType].searchSuggestions = [
        suggestion,
        ...state[entityType].searchSuggestions.filter(
          (sugg) => sugg !== suggestion
        ),
      ].slice(0, SUGGESTIONS_SET_LIMIT);
    },
  },
});

export const { addSearchSuggestion } = searchSuggestionsSlice.actions;

export default searchSuggestionsSlice.reducer;
