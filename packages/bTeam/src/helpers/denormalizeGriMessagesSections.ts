import { Message } from "../types/message";
import dayjs from "dayjs";

type SectionListData = {
  title: string;
  data: Message[];
};

export const denormalizeGriMessagesSections = (
  gridMessages: { byId: Record<string, Message>; allIds: string[] },
  selectedFolderEmailIds: string[]
): SectionListData[] => {
  const { byId } = gridMessages;

  // Create an array of messages from normalized data, filtering out any undefined messages
  const messages: Message[] =
    selectedFolderEmailIds?.map((id) => byId[id])?.filter(Boolean) || [];

  // Sort messages by sentDate in descending order (newest first)
  const sortedMessages = messages.sort((a, b) => {
    const dateA = dayjs(a.sentDate);
    const dateB = dayjs(b.sentDate);
    return dateB.valueOf() - dateA.valueOf(); // Descending order
  });

  // Group messages by a formatted date using dayjs
  const sections: { [key: string]: Message[] } = {};

  sortedMessages.forEach((message) => {
    const sectionKey = dayjs(message.sentDate).format("DD MMM YYYY"); // Formatting as 'DD MMM YYYY'
    if (!sections[sectionKey]) {
      sections[sectionKey] = [];
    }
    sections[sectionKey].push(message);
  });

  // Transform the sections object into an array of SectionListData
  // Sort sections by date in descending order (newest dates first)
  const sectionEntries = Object.entries(sections).sort(([keyA], [keyB]) => {
    const dateA = dayjs(keyA, "DD MMM YYYY");
    const dateB = dayjs(keyB, "DD MMM YYYY");
    return dateB.valueOf() - dateA.valueOf(); // Descending order
  });

  return sectionEntries.map(([key, data]) => ({
    title: key,
    data: data, // Messages within each section are already sorted
  }));
};
