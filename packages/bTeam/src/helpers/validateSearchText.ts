/**
 Validation rules:
 1. Quotes must be in pairs.
 2. Parenthesis outside of quotes must be in pairs.
 3. Please use quotes if you want to search with reserved words: near, and, or, not.
 */
export const validateSearchText = (searchText: string): string | null => {
  // If search text is empty, validation passes
  if (!searchText || searchText.trim() === "") {
    return "";
  }

  // Check for unmatched quotes
  const quoteCount = (searchText.match(/"/g) || []).length;
  if (quoteCount % 2 !== 0) {
    return "Quotes must be in pairs!";
  }

  // Extract text outside of quotes for reserved word checking
  let textToCheck = searchText;
  const quotedTexts: string[] = [];

  // Extract all quoted text
  const quoteRegex = /"([^"]*)"/g;
  let match;
  while ((match = quoteRegex.exec(searchText)) !== null) {
    quotedTexts.push(match[0]); // Save the full quoted text including quotes
  }

  // Remove quoted text from the string to check
  quotedTexts.forEach((quotedText) => {
    textToCheck = textToCheck.replace(quotedText, "");
  });

  // Check for unmatched parentheses outside of quotes
  let parenthesesCount = 0;

  // Count parentheses
  for (let i = 0; i < textToCheck.length; i++) {
    const char = textToCheck[i];

    if (char === "(") {
      parenthesesCount++;
    } else if (char === ")") {
      parenthesesCount--;
    }

    // If count goes negative, we have a closing parenthesis without a matching opening one
    if (parenthesesCount < 0) {
      return "Parenthesis outside of quotes must be in pairs!";
    }
  }

  // Check if any parenthesis is unmatched (count should be 0)
  if (parenthesesCount !== 0) {
    return "Parenthesis outside of quotes must be in pairs!";
  }

  // Check for unquoted reserved words in the remaining text
  const reservedWords = ["near", "end", "and", "or", "not"];
  const textToCheckLower = textToCheck.toLowerCase();

  // Check if any reserved word is present as a standalone word
  const containsReservedWord = reservedWords.some((word) => {
    // Create regex to match the word as a whole word, not as part of another word
    const regex = new RegExp(`\\b${word}\\b`, "i");
    return regex.test(textToCheckLower);
  });

  if (containsReservedWord) {
    return "Please use quotes if you want to search with reserved words: near, and, or, not.";
  }

  return null;
};

// Escapes quoted content in search text by adding backslash before quotes
export const escapeQuotedContent = (searchText: string): string => {
  if (!searchText) {
    return "";
  }

  // Add a backslash before each double quote
  return searchText.replace(/"/g, '\"');
};
