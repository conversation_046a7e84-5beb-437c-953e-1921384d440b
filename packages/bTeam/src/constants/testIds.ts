// testId structure: "transfer__genericErrorModal--leftContent"

export const TEST_IDS = {
  loginLoader: "login--loader",

  inboxComposeIcon: "inbox--composeIcon",
  inboxHeaderBurgerIcon: "inbox__header--burgerIcon",
  inboxSearchInput: "inbox--searchInput",

  composeHeaderCloseButton: "compose__header--closeButton",
  composeHeaderAddAttachmentButton: "compose__header--addAttachmentButton",
  composeHeaderSaveDraftButton: "compose__header--saveDraftButton",
  composeHeaderSendMessageButton: "compose__header--sendMessageButton",
  composeHeaderAttachmentCount: "compose__header--attachmentCount",

  composeFieldFrom: "compose__fields--from",
  composeFieldTo: "compose__fields--to",
  composeFieldCc: "compose__fields--cc",
  composeFieldBcc: "compose__fields--bcc",
  composeFieldSubject: "compose__fields--subject",
  composeFieldBody: "compose__fields--body",
  composeExpandFieldsButton: "compose--expandFieldsButton",
  composeFieldFromErrorMessage: "compose__fields--fromErrorMessage",
  composeFieldCcErrorMessage: "compose__fields--ccErrorMessage",
  composeFieldBccErrorMessage: "compose__fields--bccErrorMessage",

  selectRecipientsBottomSheetAddButton:
    "selectRecipientsBottomSheet--addButton",
  selectRecipientsBottomSheetSearchInput:
    "selectRecipientsBottomSheet--searchInput",

  inboxSearchFiltersFieldsKeywords: "inboxSearchFilters__fields--keywords",
  inboxSearchFiltersFieldsInOut: "inboxSearchFilters__fields--inOut",
  inboxSearchFiltersFieldsFrom: "inboxSearchFilters__fields--from",
  inboxSearchFiltersFieldsTo: "inboxSearchFilters__fields--to",
  inboxSearchFiltersFieldsDatePeriod: "inboxSearchFilters__fields--datePeriod",
  inboxSearchFiltersFieldsFlagged: "inboxSearchFilters__fields--flagged",
  inboxSearchFiltersFieldsHasAttachments:
    "inboxSearchFilters__fields--hasAttachments",
  inboxSearchFiltersFieldsReadUnread: "inboxSearchFilters__fields--readUnread",
  inboxSearchFiltersApplyButton: "inboxSearchFilters--applyButton",

  messageAttachmentTabEmptyMessage: "message__attachmentTab--emptyMessage",
  messageAttachmentTabTabBarIcon: "message__attachmentTab--tabBarIcon",
  messageNavigationHeaderViewedIcon: "message__navigationHeader--viewedIcon",
  messageNavigationHeaderNotViewedIcon:
    "message__navigationHeader--notViewedIcon",
  messageNavigationHeaderFlaggedIcon: "message__navigationHeader--flaggedIcon",
  messageNavigationHeaderNotFlaggedIcon:
    "message__navigationHeader--notFlaggedIcon",
  messageNavigationHeaderUndoIcon: "message__navigationHeader--undoIcon",
  messageNavigationHeaderTrashIcon: "message__navigationHeader--trashIcon",
  messageDeleteModalDeleteOrRestoreButton:
    "message__deleteModal--deleteOrRestoreButton",
  messageDeleteModalCancelButton: "message__deleteModal--cancelButton",

  inboxBottomSheetMarkAsReadButton: "inbox__bottomSheet--markAsReadButton",
  inboxBottomSheetDeleteButton: "inbox__bottomSheet--deleteButton",
  inboxBottomSheetMoreButton: "inbox__bottomSheet--moreButton",
  inboxBottomSheetMarkAsReadMoreButton:
    "inbox__bottomSheet--markAsReadMoreButton",
  inboxBottomSheetMoveToFolderButton: "inbox__bottomSheet--moveToFolderButton",
  inboxBottomSheetCopyToFolderButton: "inbox__bottomSheet--copyToFolderButton",
  inboxBottomSheetLinkToCaseButton: "inbox__bottomSheet--linkToCaseButton",
  inboxBottomSheetFlagButton: "inbox__bottomSheet--flagButton",
  inboxBottomSheetSelectAllButton: "inbox__bottomSheet--selectAllButton",
  inboxBottomSheetFindRelatedButton: "inbox__bottomSheet--findRelatedButton",
  inboxBottomSheetUnSelectAllButton: "inbox__bottomSheet--unSelectAllButton",
  inboxNavigationHeaderSelectAllCheckbox: "inbox__navigationHeader--selectAllCheckbox",

  foldersListMoveButton: "foldersList--moveButton",
};
